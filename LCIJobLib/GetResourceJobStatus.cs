/*
 * =====================================================
 * LCI Job Library - Get Resource Job Status
 * =====================================================
 *
 * Purpose: Shows what jobs are currently logged into/being worked on for each resource per site
 *          and returns the associated next job for the machine as scheduled in Epicor
 *
 * Parameters:
 *   - siteFilter (string): Optional site filter - Plant/Site ID from JobHead (null or empty for all sites)
 *
 * Returns:
 *   - result (System.Data.DataSet): DataSet containing three tables:
 *     1. CurrentJobs - Jobs currently being worked on
 *     2. NextJobs - Next scheduled job for each resource
 *     3. ResourceSummary - Combined view per resource
 *   - errorMessage (string): Error information if any issues occur
 *
 * Company: Hardcoded to 162250 (LCI)
 *
 * Process:
 *   1. Query active labor transactions to find current jobs being worked on
 *   2. Query job operations to find next scheduled jobs for each resource
 *   3. Build comprehensive DataSet with current and next job information
 *   4. Include resource, job, part, and employee details
 *
 * Tables Used:
 *   - Resource: Resource master data
 *   - LaborDtl: Labor detail transactions (ActiveTrans = 1 for current jobs)
 *   - JobHead: Job header information
 *   - JobOper: Job operation details, scheduling, and resource assignments (engineering routing)
 *   - Part: Part master data
 *   - EmpBasic: Employee information
 *
 * Usage Example:
 *   siteFilter = null;  // All sites, or "MAIN" for specific Plant/Site ID from jobs
 *   // Execute function
 *   // result DataSet will contain CurrentJobs, NextJobs, and ResourceSummary tables
 *   // errorMessage will contain any error details
 *
 * Note: Resources are site-independent. Site information comes from the JobHead.Plant field.
 */

// Initialize variables
string companyID = "162250";           // LCI company ID
result = new DataSet("ResourceJobStatus");  // Initialize result DataSet
errorMessage = "";                     // Initialize error message

try
{
    // =================================================================
    // Create DataSet Structure
    // =================================================================
    
    // Create CurrentJobs table structure
    DataTable currentJobsTable = new DataTable("CurrentJobs");
    currentJobsTable.Columns.Add("Site", typeof(string));
    currentJobsTable.Columns.Add("ResourceID", typeof(string));
    currentJobsTable.Columns.Add("ResourceDescription", typeof(string));
    currentJobsTable.Columns.Add("JobNum", typeof(string));
    currentJobsTable.Columns.Add("PartNum", typeof(string));
    currentJobsTable.Columns.Add("PartDescription", typeof(string));
    currentJobsTable.Columns.Add("AssemblySeq", typeof(int));
    currentJobsTable.Columns.Add("OprSeq", typeof(int));
    currentJobsTable.Columns.Add("OperationDescription", typeof(string));
    currentJobsTable.Columns.Add("StartTime", typeof(DateTime));
    currentJobsTable.Columns.Add("EmployeeID", typeof(string));
    currentJobsTable.Columns.Add("EmployeeName", typeof(string));
    currentJobsTable.Columns.Add("Status", typeof(string));
    
    // Create NextJobs table structure
    DataTable nextJobsTable = new DataTable("NextJobs");
    nextJobsTable.Columns.Add("Site", typeof(string));
    nextJobsTable.Columns.Add("ResourceID", typeof(string));
    nextJobsTable.Columns.Add("ResourceDescription", typeof(string));
    nextJobsTable.Columns.Add("JobNum", typeof(string));
    nextJobsTable.Columns.Add("PartNum", typeof(string));
    nextJobsTable.Columns.Add("PartDescription", typeof(string));
    nextJobsTable.Columns.Add("AssemblySeq", typeof(int));
    nextJobsTable.Columns.Add("OprSeq", typeof(int));
    nextJobsTable.Columns.Add("OperationDescription", typeof(string));
    nextJobsTable.Columns.Add("ScheduledStartTime", typeof(DateTime));
    nextJobsTable.Columns.Add("ScheduledEndTime", typeof(DateTime));
    nextJobsTable.Columns.Add("EstimatedHours", typeof(decimal));
    
    // Create ResourceSummary table structure
    DataTable summaryTable = new DataTable("ResourceSummary");
    summaryTable.Columns.Add("Site", typeof(string));
    summaryTable.Columns.Add("ResourceID", typeof(string));
    summaryTable.Columns.Add("ResourceDescription", typeof(string));
    summaryTable.Columns.Add("CurrentJobNum", typeof(string));
    summaryTable.Columns.Add("CurrentPartNum", typeof(string));
    summaryTable.Columns.Add("CurrentStatus", typeof(string));
    summaryTable.Columns.Add("NextJobNum", typeof(string));
    summaryTable.Columns.Add("NextPartNum", typeof(string));
    summaryTable.Columns.Add("NextScheduledStart", typeof(DateTime));
    
    // =================================================================
    // Query Current Active Jobs
    // =================================================================
    
    // Get current jobs being worked on (active labor transactions)
    var currentJobs = (from r in Db.Resource
                      join ld in Db.LaborDtl on r.ResourceID equals ld.ResourceID into ldGroup
                      from ld in ldGroup.DefaultIfEmpty()
                      join jh in Db.JobHead on ld.JobNum equals jh.JobNum into jhGroup
                      from jh in jhGroup.DefaultIfEmpty()
                      join jo in Db.JobOper on new { ld.JobNum, ld.AssemblySeq, ld.OprSeq } 
                                              equals new { jo.JobNum, jo.AssemblySeq, jo.OprSeq } into joGroup
                      from jo in joGroup.DefaultIfEmpty()
                      join p in Db.Part on jh.PartNum equals p.PartNum into pGroup
                      from p in pGroup.DefaultIfEmpty()
                      join emp in Db.EmpBasic on ld.EmployeeNum equals emp.EmpID into empGroup
                      from emp in empGroup.DefaultIfEmpty()
                      where r.Company == companyID
                        && r.Inactive == false
                        && (ld == null || ld.ActiveTrans == true)
                        && (string.IsNullOrEmpty(siteFilter) || jh == null || jh.Plant == siteFilter)
                      select new
                      {
                          Site = jh != null ? jh.Plant ?? "" : "",
                          ResourceID = r.ResourceID ?? "",
                          ResourceDescription = r.Description ?? "",
                          JobNum = jh != null ? jh.JobNum : "",
                          PartNum = jh != null ? jh.PartNum : "",
                          PartDescription = p != null ? p.PartDescription : "",
                          AssemblySeq = ld != null ? ld.AssemblySeq : 0,
                          OprSeq = ld != null ? ld.OprSeq : 0,
                          OperationDescription = jo != null ? jo.OpDesc : "",
                          StartTime = ld != null && ld.ClockInDate.HasValue ? ld.ClockInDate : (DateTime?)null,
                          EmployeeID = ld != null ? ld.EmployeeNum : "",
                          EmployeeName = emp != null ? emp.Name : "",
                          Status = ld != null && ld.ActiveTrans == true ? "Active" : "Available"
                      }).ToList();
    
    // Populate CurrentJobs table
    foreach (var job in currentJobs)
    {
        DataRow row = currentJobsTable.NewRow();
        row["Site"] = job.Site;
        row["ResourceID"] = job.ResourceID;
        row["ResourceDescription"] = job.ResourceDescription;
        row["JobNum"] = job.JobNum;
        row["PartNum"] = job.PartNum;
        row["PartDescription"] = job.PartDescription;
        row["AssemblySeq"] = job.AssemblySeq;
        row["OprSeq"] = job.OprSeq;
        row["OperationDescription"] = job.OperationDescription;
        row["StartTime"] = job.StartTime ?? (object)DBNull.Value;
        row["EmployeeID"] = job.EmployeeID;
        row["EmployeeName"] = job.EmployeeName;
        row["Status"] = job.Status;
        currentJobsTable.Rows.Add(row);
    }
    
    // =================================================================
    // Query Next Scheduled Jobs
    // =================================================================

    // Get next scheduled jobs - use history-based approach with operation sequencing
    // Since JobOper.ResourceID field name is not accessible in LINQ, use labor history
    var nextJobs = (from jo in Db.JobOper
                   join jh in Db.JobHead on jo.JobNum equals jh.JobNum
                   join p in Db.Part on jh.PartNum equals p.PartNum into pGroup
                   from p in pGroup.DefaultIfEmpty()
                   where jh.Company == companyID
                     && jh.JobClosed == false
                     && jo.OpComplete == false
                     && jo.StartDate >= DateTime.Today
                     && (string.IsNullOrEmpty(siteFilter) || jh.Plant == siteFilter)
                     && !Db.LaborDtl.Any(ld => ld.JobNum == jo.JobNum
                                            && ld.AssemblySeq == jo.AssemblySeq
                                            && ld.OprSeq == jo.OprSeq
                                            && ld.ActiveTrans == true)
                     // Ensure all previous operations in the same assembly are complete
                     && !Db.JobOper.Any(prevOp => prevOp.JobNum == jo.JobNum
                                                && prevOp.AssemblySeq == jo.AssemblySeq
                                                && prevOp.OprSeq < jo.OprSeq
                                                && prevOp.OpComplete == false)
                   // Get the most recent resource used for this exact operation
                   let exactResource = Db.LaborDtl
                                        .Where(ld => ld.JobNum == jo.JobNum
                                                  && ld.AssemblySeq == jo.AssemblySeq
                                                  && ld.OprSeq == jo.OprSeq)
                                        .OrderByDescending(ld => ld.ClockInDate)
                                        .Select(ld => ld.ResourceID)
                                        .FirstOrDefault()
                   // If no exact match, find resource that has done similar operations (same OpCode)
                   let similarResource = string.IsNullOrEmpty(exactResource) ?
                                        Db.LaborDtl
                                        .Join(Db.JobOper, ld => new { ld.JobNum, ld.AssemblySeq, ld.OprSeq },
                                                         op => new { op.JobNum, op.AssemblySeq, op.OprSeq },
                                                         (ld, op) => new { ld, op })
                                        .Where(x => x.op.OpCode == jo.OpCode && !string.IsNullOrEmpty(x.ld.ResourceID))
                                        .OrderByDescending(x => x.ld.ClockInDate)
                                        .Select(x => x.ld.ResourceID)
                                        .FirstOrDefault() : exactResource
                   let finalResourceID = similarResource ?? ""
                   let resource = Db.Resource.FirstOrDefault(r => r.ResourceID == finalResourceID
                                                                && r.Company == companyID
                                                                && r.Inactive == false)
                   where !string.IsNullOrEmpty(finalResourceID) && resource != null
                   select new
                   {
                       Site = jh.Plant ?? "",
                       ResourceID = resource.ResourceID ?? "",
                       ResourceDescription = resource.Description ?? "",
                       JobNum = jo.JobNum ?? "",
                       PartNum = jh.PartNum ?? "",
                       PartDescription = p != null ? p.PartDescription : "",
                       AssemblySeq = jo.AssemblySeq,
                       OprSeq = jo.OprSeq,
                       OperationDescription = jo.OpDesc ?? "",
                       ScheduledStartTime = jo.StartDate,
                       ScheduledEndTime = jo.DueDate,
                       EstimatedHours = jo.EstSetHours + jo.EstProdHours
                   })
                   .GroupBy(x => x.ResourceID)
                   .Select(g => g.OrderBy(x => x.ScheduledStartTime).FirstOrDefault())
                   .Where(x => x != null)
                   .ToList();
    
    // Populate NextJobs table
    foreach (var job in nextJobs)
    {
        DataRow row = nextJobsTable.NewRow();
        row["Site"] = job.Site;
        row["ResourceID"] = job.ResourceID;
        row["ResourceDescription"] = job.ResourceDescription;
        row["JobNum"] = job.JobNum;
        row["PartNum"] = job.PartNum;
        row["PartDescription"] = job.PartDescription;
        row["AssemblySeq"] = job.AssemblySeq;
        row["OprSeq"] = job.OprSeq;
        row["OperationDescription"] = job.OperationDescription;
        row["ScheduledStartTime"] = job.ScheduledStartTime ?? (object)DBNull.Value;
        row["ScheduledEndTime"] = job.ScheduledEndTime ?? (object)DBNull.Value;
        row["EstimatedHours"] = job.EstimatedHours;
        nextJobsTable.Rows.Add(row);
    }
    
    // =================================================================
    // Create Resource Summary
    // =================================================================
    
    // Get all unique resources and combine current/next job info
    var uniqueResources = currentJobs.Select(j => new { j.Site, j.ResourceID, j.ResourceDescription })
                                    .Union(nextJobs.Select(j => new { j.Site, j.ResourceID, j.ResourceDescription }))
                                    .Distinct()
                                    .OrderBy(r => r.Site)
                                    .ThenBy(r => r.ResourceID);
    
    foreach (var resource in uniqueResources)
    {
        var currentJob = currentJobs.FirstOrDefault(j => j.ResourceID == resource.ResourceID && j.Status == "Active");
        var nextJob = nextJobs.FirstOrDefault(j => j.ResourceID == resource.ResourceID);
        
        DataRow row = summaryTable.NewRow();
        row["Site"] = resource.Site;
        row["ResourceID"] = resource.ResourceID;
        row["ResourceDescription"] = resource.ResourceDescription;
        row["CurrentJobNum"] = currentJob?.JobNum ?? "";
        row["CurrentPartNum"] = currentJob?.PartNum ?? "";
        row["CurrentStatus"] = currentJob?.Status ?? "Available";
        row["NextJobNum"] = nextJob?.JobNum ?? "";
        row["NextPartNum"] = nextJob?.PartNum ?? "";
        row["NextScheduledStart"] = nextJob?.ScheduledStartTime ?? (object)DBNull.Value;
        summaryTable.Rows.Add(row);
    }
    
    // Add tables to DataSet
    result.Tables.Add(currentJobsTable);
    result.Tables.Add(nextJobsTable);
    result.Tables.Add(summaryTable);
}
catch (Exception ex)
{
    // Handle any errors and provide detailed error information
    errorMessage = "Error retrieving resource job status: " + ex.Message;
    
    // Create error table in DataSet
    DataTable errorTable = new DataTable("Error");
    errorTable.Columns.Add("ErrorMessage", typeof(string));
    errorTable.Columns.Add("ErrorDetails", typeof(string));
    
    DataRow errorRow = errorTable.NewRow();
    errorRow["ErrorMessage"] = "Error retrieving resource job status";
    errorRow["ErrorDetails"] = ex.Message;
    errorTable.Rows.Add(errorRow);
    
    result.Tables.Clear();
    result.Tables.Add(errorTable);
}
