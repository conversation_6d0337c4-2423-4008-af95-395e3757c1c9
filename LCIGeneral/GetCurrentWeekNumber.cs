using System;
using System.Globalization;

/*
 * =====================================================
 * LCI General Library - Get Current Week Number
 * =====================================================
 * 
 * Purpose: Returns the current week number of the year using ISO 8601 standard
 * 
 * Parameters: None
 * 
 * Returns:
 *   - result (int): Current week number (1-53) based on ISO 8601 standard
 * 
 * Notes:
 *   - Uses ISO 8601 week numbering system where:
 *     * Week 1 is the first week with at least 4 days in the new year
 *     * Monday is considered the first day of the week
 *     * Week numbers range from 1 to 53
 *   - This is the international standard for week numbering
 * 
 * Usage Example:
 *   // Execute function
 *   // result will contain the current week number (e.g., 30 for the 30th week of the year)
 */

// Initialize result
result = 1;  // Default to week 1

try
{
    // Get current date
    DateTime currentDate = DateTime.Now;
    
    // Use ISO 8601 week numbering system
    // This uses the Calendar.GetWeekOfYear method with ISO 8601 rules
    Calendar calendar = CultureInfo.InvariantCulture.Calendar;
    
    // ISO 8601 rules:
    // - Week 1 is the first week with at least 4 days in the new year
    // - Monday is the first day of the week
    result = calendar.GetWeekOfYear(
        currentDate,
        CalendarWeekRule.FirstFourDayWeek,  // Week 1 has at least 4 days in the new year
        DayOfWeek.Monday                    // Monday is first day of week
    );
}
catch (Exception ex)
{
    // If any error occurs, default to week 1
    // In a production environment, you might want to log this error
    result = 1;
}
